package task

import (
	"context"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"math/big"
	"strconv"
	"strings"
	"time"

	"github.com/blocto/solana-go-sdk/client"
	"github.com/blocto/solana-go-sdk/common"
	"github.com/blocto/solana-go-sdk/program/system"
	"github.com/blocto/solana-go-sdk/types"
	"github.com/google/uuid"
	"github.com/nats-io/nats.go"
	"github.com/shopspring/decimal"
	"github.com/tkhq/go-sdk"
	"github.com/tkhq/go-sdk/pkg/api/client/signing"
	"github.com/tkhq/go-sdk/pkg/api/models"
	"github.com/tkhq/go-sdk/pkg/apikey"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"
	"go.uber.org/zap"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	eth_common "github.com/ethereum/go-ethereum/common"
	eth_types "github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/ethereum/go-ethereum/rlp"
)

const ARB_USDC_CONTRACT_ADDRESS = "******************************************"

func ConsumeRewardClaimEvent(msg *nats.Msg) error {
	logger := global.GVA_LOG

	// Initialize reward claim result repository
	rewardClaimResultRepo := repo.NewRewardClaimResultRepository()

	// todo: move to env
	apiKey, err := apikey.FromTurnkeyPrivateKey("112e72cf9efd07c94c2cedf5a87c5098eba58a464f00005c2ef1bbd79cb3c4f0", apikey.SchemeP256)
	if err != nil {
		log.Fatal("creating API key: %w", err)
	}

	turnkeyClient, err := sdk.New(sdk.WithAPIKey(apiKey))
	if err != nil {
		logger.Error("failed to create turnkey client: %v", zap.Error(err))
		return err
	}

	var event RewardClaimEvent
	if err := json.Unmarshal(msg.Data, &event); err != nil {
		logger.Error("failed to unmarshal: %v", zap.Error(err))
		return err
	}

	// Update claim result to PROCESSING status at start
	if err := updateRewardClaimResult(rewardClaimResultRepo, event.ID, model.RewardClaimResultProcessing, "", nil); err != nil {
		logger.Error("failed to update reward claim result to PROCESSING",
			zap.String("claim_id", event.ID.String()),
			zap.Error(err))
		// Continue processing even if status update fails
	}

	var processingErr error
	var transactionHash string

	// Process the claim based on chain type
	if event.ChainID == (int)(utils.ChainIDSolanaInt) {
		transactionHash, processingErr = ProcessSolanaRewardClaim(turnkeyClient, event)
	} else if event.ChainID == (int)(utils.ChainIDArbitrumOneInt) {
		transactionHash, processingErr = ProcessErc20RewardClaim(turnkeyClient, event)
	} else {
		processingErr = fmt.Errorf("unsupported chain: %s", event.ChainID)
	}

	// Update claim result based on processing outcome
	if processingErr != nil {
		// Update result to FAILED with error message
		if err := updateRewardClaimResult(rewardClaimResultRepo, event.ID, model.RewardClaimResultFailed, processingErr.Error(), nil); err != nil {
			logger.Error("failed to update reward claim result to FAILED",
				zap.String("claim_id", event.ID.String()),
				zap.Error(err))
		}
	} else {
		// Update result to SUCCESS
		if err := updateRewardClaimResult(rewardClaimResultRepo, event.ID, model.RewardClaimResultSuccess, "", &transactionHash); err != nil {
			logger.Error("failed to update reward claim result to SUCCESS",
				zap.String("claim_id", event.ID.String()),
				zap.Error(err))
		}
	}

	// PublishResultRewardExecution(event, &processingErr)
	return processingErr
}

func ProcessSolanaRewardClaim(turnkeyClient *sdk.Client, event RewardClaimEvent) (string, error) {
	logger := global.GVA_LOG

	payerAddress := global.GVA_CONFIG.Turnkey.RewardWalletSol
	rootOrganizationID := global.GVA_CONFIG.Turnkey.OrganizationID

	if payerAddress == "" {
		return "", fmt.Errorf("Turnkey Solana wallet address is not configured, please set turnkey.reward-wallet-sol in config.yaml")
	}
	if rootOrganizationID == "" {
		return "", fmt.Errorf("Turnkey organization ID is not configured, please set turnkey.organization_id in config.yaml")
	}

	logger.Info("Using Turnkey Configuration",
		zap.String("payer_address", payerAddress),
		zap.String("organization_id", rootOrganizationID))

	c := client.NewClient("https://radial-wider-friday.solana-mainnet.quiknode.pro/11955779784cae2b94fd3195d9cbf1bf157a5cb0/")
	tx, err := BuildSolanaTransferTransaction(*c, event.Address, event.Amount)
	if err != nil {
		logger.Error("failed to build tx: %v", zap.Error(err))
		return "", err
	}

	unsigedTx, err := tx.Serialize()
	if err != nil {
		logger.Error("failed to serialize tx: %v", zap.Error(err))
		return "", err
	}
	hexTx := hex.EncodeToString(unsigedTx)

	transactionType := models.TransactionTypeEthereum
	if event.ChainID == 501424 {
		transactionType = models.TransactionTypeSolana
	}
	timestamp := time.Now().UnixMilli()
	timestampString := strconv.FormatInt(timestamp, 10)

	pkParams := signing.NewSignTransactionParams().WithBody(&models.SignTransactionRequest{
		OrganizationID: &rootOrganizationID,
		TimestampMs:    &timestampString,
		Parameters: &models.SignTransactionIntentV2{
			UnsignedTransaction: &hexTx,
			SignWith:            &payerAddress,
			Type:                transactionType.Pointer(),
		},
		Type: (*string)(models.ActivityTypeSignTransactionV2.Pointer()),
	})

	signResp, err := turnkeyClient.V0().Signing.SignTransaction(pkParams, turnkeyClient.Authenticator)
	if err != nil {
		logger.Error("failed to sign tx: %v", zap.Error(err))
		return "", err
	}

	if signResp.Payload.Activity.Result.SignTransactionResult.SignedTransaction == nil {
		logger.Error("failed to sign tx: signed transaction is nil")
		return "", fmt.Errorf("signed transaction is nil")
	}

	signedTxBytes, err := hex.DecodeString(*signResp.Payload.Activity.Result.SignTransactionResult.SignedTransaction)

	if err != nil {
		logger.Error("failed to decode signed tx: %v", zap.Error(err))
	}

	singedTx, err := types.TransactionDeserialize(signedTxBytes)

	ctx, cancel := context.WithTimeout(context.Background(), 70*time.Second)
	defer cancel()

	if err != nil {
		logger.Error("build tx: ", zap.Error(err))
		return "", err
	}

	sig, err := c.SendTransactionWithConfig(ctx, singedTx, client.SendTransactionConfig{
		SkipPreflight: false,
	})

	if err != nil {
		logger.Error("Send transaction failed:", zap.Error(err))
		// todo : translate error code
		return "", err
	}

	err = WaitForConfirmation(c, sig, 60*time.Second)
	if err != nil {
		logger.Error("Failed to confirm tx:", zap.String("tx", sig), zap.Error(err))
		return "", err
	}

	return sig, nil
}

func BuildSolanaTransferTransaction(c client.Client, address string, amount decimal.Decimal) (*types.Transaction, error) {
	ctx := context.Background()

	bh, err := c.GetLatestBlockhash(ctx)
	if err != nil {
		log.Fatalf("get latest blockhash: %v", err)
	}
	to := common.PublicKeyFromString(address)
	payerAddress := global.GVA_CONFIG.Turnkey.RewardWalletSol
	payerPk := common.PublicKeyFromString(payerAddress)
	// calculate uint64 number by amount * 1e9
	lamports := uint64(amount.Mul(decimal.NewFromInt(1e9)).IntPart())

	ixs := []types.Instruction{
		system.Transfer(system.TransferParam{
			From:   payerPk,
			To:     to,
			Amount: lamports,
		}),
	}

	// ----- 5) Assemble + sign transaction
	tx, err := types.NewTransaction(types.NewTransactionParam{
		Message: types.NewMessage(types.NewMessageParam{
			FeePayer:        payerPk,
			RecentBlockhash: bh.Blockhash,
			Instructions:    ixs,
		}),
	})

	if err != nil {
		log.Fatalf("build tx: %v", err)
		return nil, err
	}

	return &tx, nil
}

func WaitForConfirmation(c *client.Client, sig string, timeout time.Duration) error {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	ticker := time.NewTicker(500 * time.Millisecond)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return errors.New("transaction confirmation timeout after " + timeout.String())
		case <-ticker.C:
			res, err := c.GetTransaction(ctx, sig)
			if err != nil {
				// retry if RPC not responsive
				continue
			}
			if res != nil {
				if res.Meta != nil && res.Meta.Err == nil {
					// success
					global.GVA_LOG.Info("Transaction confirmed:", zap.String("tx", sig))
					return nil
				}
				if res.Meta != nil && res.Meta.Err != nil {
					// tx failed
					return fmt.Errorf("transaction failed: %v", res.Meta.Err)
				}
			}
		}
	}
}

const ERC20ABI = `[
	{"constant":false,"inputs":[{"name":"recipient","type":"address"},{"name":"amount","type":"uint256"}],"name":"transfer","outputs":[{"name":"","type":"bool"}],"type":"function"},
	{"constant":true,"inputs":[{"name":"account","type":"address"}],"name":"balanceOf","outputs":[{"name":"","type":"uint256"}],"type":"function"}
]`

func ProcessErc20RewardClaim(turnkeyClient *sdk.Client, event RewardClaimEvent) (string, error) {
	logger := global.GVA_LOG
	payerAddress := global.GVA_CONFIG.Turnkey.RewardWalletEvm
	rootOrganizationID := global.GVA_CONFIG.Turnkey.OrganizationID

	if payerAddress == "" {
		return "", fmt.Errorf("Turnkey EVM wallet address is not configured, please set turnkey.reward-wallet-evm in config.yaml")
	}
	if rootOrganizationID == "" {
		return "", fmt.Errorf("Turnkey organization ID is not configured, please set turnkey.organization_id in config.yaml")
	}

	logger.Info("Using Turnkey Configuration",
		zap.String("payer_address", payerAddress),
		zap.String("organization_id", rootOrganizationID))

	usdcAddress := eth_common.HexToAddress(ARB_USDC_CONTRACT_ADDRESS)
	receiptAddress := eth_common.HexToAddress(event.Address)

	// ---- connect Ethereum RPC
	ethClient, err := ethclient.Dial("https://arb1.arbitrum.io/rpc")
	if err != nil {
		return "", fmt.Errorf("failed to connect to Ethereum: %w", err)
	}

	// ---- Encode ERC20 transfer(to, amount)
	parsedABI, err := abi.JSON(strings.NewReader(ERC20ABI))
	if err != nil {
		logger.Error("failed to parse ABI: ", zap.Error(err))
		return "", fmt.Errorf("parse ABI: %w", err)
	}

	amount := new(big.Int)
	amount = big.NewInt(event.Amount.Mul(decimal.NewFromInt(1e6)).IntPart()) // USDC has 6 decimals

	input, err := parsedABI.Pack("transfer", receiptAddress, amount)
	if err != nil {
		logger.Error("failed to pack transfer: ", zap.Error(err))
		return "", fmt.Errorf("pack transfer: %w", err)
	}

	// ---- Build tx
	fromAddr := eth_common.HexToAddress(payerAddress)

	// Check USDC balance before transfer
	balanceCaller, err := parsedABI.Pack("balanceOf", fromAddr)
	if err != nil {
		logger.Error("failed to pack balanceOf: ", zap.Error(err))
		return "", fmt.Errorf("pack balanceOf: %w", err)
	}

	balanceResult, err := ethClient.CallContract(context.Background(), ethereum.CallMsg{
		To:   &usdcAddress,
		Data: balanceCaller,
	}, nil)
	if err != nil {
		logger.Error("failed to call balanceOf: ", zap.Error(err))
		return "", fmt.Errorf("call balanceOf: %w", err)
	}

	balance := new(big.Int).SetBytes(balanceResult)
	if balance.Cmp(amount) < 0 {
		logger.Error("insufficient USDC balance",
			zap.String("required", amount.String()),
			zap.String("available", balance.String()),
			zap.String("wallet", payerAddress))
		return "", fmt.Errorf("insufficient USDC balance: required %s, available %s", amount.String(), balance.String())
	}

	logger.Info("USDC balance check passed",
		zap.String("required", amount.String()),
		zap.String("available", balance.String()),
		zap.String("wallet", payerAddress))

	nonce, err := ethClient.PendingNonceAt(context.Background(), fromAddr)
	if err != nil {
		logger.Error("failed to get nonce: ", zap.Error(err))
		return "", fmt.Errorf("get nonce: %w", err)
	}

	// Get current gas price
	gasPrice, err := ethClient.SuggestGasPrice(context.Background())
	if err != nil {
		logger.Error("failed to get gas price: ", zap.Error(err))
		return "", fmt.Errorf("get gas price: %w", err)
	}

	// Estimate gas
	msg := ethereum.CallMsg{
		From: fromAddr, To: &usdcAddress, Data: input,
	}
	gasLimit, err := ethClient.EstimateGas(context.Background(), msg)
	if err != nil {
		logger.Error("failed to estimate gas: ", zap.Error(err))
		// return fmt.Errorf("estimate gas: %w", err)
		gasLimit = uint64(100000)
	}

	// tx := eth_types.NewTransaction(nonce, usdcAddress, big.NewInt(0), gasLimit, nil, input)
	tx := eth_types.NewTransaction(nonce, usdcAddress, big.NewInt(0), gasLimit, gasPrice, input)

	chainID := big.NewInt(int64(event.ChainID))
	unsignedTxBytes, err := rlp.EncodeToBytes([]interface{}{
		tx.Nonce(),
		tx.GasPrice(),
		tx.Gas(),
		tx.To(),
		tx.Value(),
		tx.Data(),
		chainID, uint(0), uint(0), // EIP-155 replay protection
	})

	if err != nil {
		logger.Error("failed to marshal tx: ", zap.Error(err))
		return "", fmt.Errorf("marshal tx: %w", err)
	}

	hexTx := hex.EncodeToString(unsignedTxBytes)

	// add prefix 0x if not exist
	if !strings.HasPrefix(hexTx, "0x") {
		hexTx = "0x" + hexTx
	}

	// ---- Ask Turnkey to sign
	transactionType := models.TransactionTypeEthereum
	timestamp := strconv.FormatInt(time.Now().UnixMilli(), 10)

	pkParams := signing.NewSignTransactionParams().WithBody(&models.SignTransactionRequest{
		OrganizationID: &rootOrganizationID,
		TimestampMs:    &timestamp,
		Parameters: &models.SignTransactionIntentV2{
			UnsignedTransaction: &hexTx,
			SignWith:            &payerAddress,
			Type:                transactionType.Pointer(),
		},
		Type: (*string)(models.ActivityTypeSignTransactionV2.Pointer()),
	})

	signResp, err := turnkeyClient.V0().Signing.SignTransaction(pkParams, turnkeyClient.Authenticator)
	if err != nil {
		logger.Error("failed to sign tx: %v", zap.Error(err))
		return "", err
	}
	if signResp.Payload.Activity.Result.SignTransactionResult.SignedTransaction == nil {
		logger.Error("failed to sign tx: signed transaction is nil")
		return "", fmt.Errorf("signed transaction is nil")
	}

	signedTxBytes, err := hex.DecodeString(*signResp.Payload.Activity.Result.SignTransactionResult.SignedTransaction)
	if err != nil {
		logger.Error("failed to decode signed tx: ", zap.Error(err))
		return "", fmt.Errorf("decode signed tx: %w", err)
	}

	var signedTx eth_types.Transaction
	if err := signedTx.UnmarshalBinary(signedTxBytes); err != nil {
		logger.Error("failed to unmarshal signed tx: ", zap.Error(err))
		return "", fmt.Errorf("unmarshal signed tx: %w", err)
	}

	err = ethClient.SendTransaction(context.Background(), &signedTx)
	if err != nil {
		logger.Error("failed to send tx: ", zap.Error(err))
		return "", fmt.Errorf("send tx: %w", err)
	}

	// ---- Wait for confirmation
	receipt, err := WaitForEthConfirmation(ethClient, signedTx.Hash(), 30*time.Second)
	if err != nil {
		logger.Error("failed to confirm tx: ", zap.Error(err))
		return "", fmt.Errorf("failed confirmation: %w", err)
	}

	if receipt.Status != 1 {
		logger.Error("transaction failed", zap.Any("receipt", receipt))
		return "", fmt.Errorf("transaction failed: %+v", receipt)
	}

	txHash := signedTx.Hash().Hex()
	logger.Info("✅ USDC ERC20 transfer confirmed", zap.String("txHash", txHash))
	return txHash, nil
}

func WaitForEthConfirmation(client *ethclient.Client, txHash eth_common.Hash, timeout time.Duration) (*eth_types.Receipt, error) {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()
	ticker := time.NewTicker(2 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return nil, fmt.Errorf("timeout after %s waiting for tx %s", timeout, txHash.Hex())
		case <-ticker.C:
			receipt, _ := client.TransactionReceipt(ctx, txHash)
			if receipt != nil {
				return receipt, nil
			}
		}
	}
}

func PublishResultRewardExecution(event RewardClaimEvent, err *error) {
	logger := global.GVA_LOG

	var errMessage string
	var errCode string
	resultStatus := RewardClaimResultSuccess

	if err != nil {
		resultStatus = RewardClaimResultFailed
		if err != nil && *err != nil {
			errMessage = (*err).Error()
			errCode = "ExecutionFailed"
		} else {
			errMessage = "Unknown error"
			errCode = "UnknownError"
		}
	}

	result := RewardClaimResultEvent{
		ID:           event.ID,
		UserID:       event.UserID,
		Address:      event.Address,
		Amount:       event.Amount,
		Token:        event.Token,
		ChainID:      event.ChainID,
		CreatedAt:    event.CreatedAt,
		Result:       resultStatus,
		ErrorMessage: &errMessage,
		ErrorCode:    &errCode,
	}

	// data, errEncode := json.Marshal(result)
	// if errEncode != nil {
	// 	logger.Error("failed to marshal result: ", zap.Error(errEncode))
	// 	return
	// }

	// if err := nc.Publish(string(RewardClaimResultEventSubject), data); err != nil {
	// 	logger.Error("failed to publish result: ", zap.Error(err))
	// 	return
	// }

	// Save the reward claim result to RewardClaimResultRecord table

	logger.Info("Published reward execution result", zap.Any("result", result))
}

// updateRewardClaimResult updates the reward claim result record
func updateRewardClaimResult(repo *repo.RewardClaimResultRepository, claimID uuid.UUID, result model.RewardClaimResult, errorMessage string, transactionHash *string) error {
	record, err := repo.GetByID(claimID)
	if err != nil {
		return fmt.Errorf("failed to get reward claim result by ID: %w", err)
	}

	// Update result
	record.Result = result

	if result == model.RewardClaimResultProcessing {
		currentTime := time.Now()
		record.ProcessedAt = &currentTime
	}

	if transactionHash != nil {
		record.TransactionHash = transactionHash
	}

	// Update error message if provided
	if errorMessage != "" {
		record.ErrorMessage = &errorMessage
	}

	// Update the record in database
	if err := repo.Update(record); err != nil {
		return fmt.Errorf("failed to update reward claim result: %w", err)
	}

	global.GVA_LOG.Info("Reward claim result updated",
		zap.String("claim_id", claimID.String()),
		zap.String("result", string(result)),
		zap.String("error_message", errorMessage))

	return nil
}

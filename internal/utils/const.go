package utils

const (
	TaskAgentReferralSnapshot = iota
	TaskLevelUpgrade
	TaskReferralTreeSnapshot
	TaskInfiniteAgentReferralTree
	TaskInfiniteAgentCommission
)

type ChainIDStringType string

const (
	ChainIDSolana ChainIDStringType = "501424"
)

type ChainIDIntType int

const (
	ChainIDSolanaInt      ChainIDIntType = 501424
	ChainIDArbitrumOneInt ChainIDIntType = 42161
)

const WSOL_ADDRESS = "So11111111111111111111111111111111111111112"
const SOL_DECIMALS = 9

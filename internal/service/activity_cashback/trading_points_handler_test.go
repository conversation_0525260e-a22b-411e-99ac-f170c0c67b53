package activity_cashback

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"go.uber.org/zap"
)

func TestTradingPointsHandler_Handle(t *testing.T) {
	// Setup global logger for testing
	global.GVA_LOG = zap.NewNop()
	tests := []struct {
		name           string
		volume         float64
		tradeType      string
		expectedPoints int
		shouldProcess  bool
		expectError    bool
	}{
		{
			name:           "MEME trade volume 1-99",
			volume:         50.0,
			tradeType:      "MEME",
			expectedPoints: 1,
			shouldProcess:  true,
			expectError:    false,
		},
		{
			name:           "MEME trade volume 100-499",
			volume:         250.0,
			tradeType:      "MEME",
			expectedPoints: 5,
			shouldProcess:  true,
			expectError:    false,
		},
		{
			name:           "MEME trade volume 500-2999",
			volume:         1500.0,
			tradeType:      "MEME",
			expectedPoints: 12,
			shouldProcess:  true,
			expectError:    false,
		},
		{
			name:           "MEME trade volume 3000-9999",
			volume:         5000.0,
			tradeType:      "MEME",
			expectedPoints: 25,
			shouldProcess:  true,
			expectError:    false,
		},
		{
			name:           "MEME trade volume 10000+",
			volume:         15000.0,
			tradeType:      "MEME",
			expectedPoints: 40,
			shouldProcess:  true,
			expectError:    false,
		},
		{
			name:           "PERPETUAL trade should be skipped",
			volume:         5000.0,
			tradeType:      "PERPETUAL",
			expectedPoints: 0,
			shouldProcess:  false,
			expectError:    false,
		},
		{
			name:           "Invalid volume",
			volume:         0.0,
			tradeType:      "MEME",
			expectedPoints: 0,
			shouldProcess:  false,
			expectError:    true,
		},
		{
			name:           "Volume below 1",
			volume:         0.5,
			tradeType:      "MEME",
			expectedPoints: 0,
			shouldProcess:  true,
			expectError:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock service
			mockService := &MockActivityCashbackService{}

			// Create handler
			handler := NewTradingPointsHandler(mockService)

			// Create test task
			taskIdentifier := model.TaskIDTradingPoints
			task := &model.ActivityTask{
				ID:             uuid.New(),
				TaskIdentifier: &taskIdentifier,
			}

			// Prepare test data
			data := map[string]interface{}{
				"volume":     tt.volume,
				"trade_type": tt.tradeType,
			}

			userID := uuid.New()
			ctx := context.Background()

			// Set up mock expectations
			if tt.shouldProcess && tt.expectedPoints > 0 && !tt.expectError {
				mockService.On("AddPoints", ctx, userID, tt.expectedPoints, mock.AnythingOfType("string")).Return(nil)
				mockService.On("IncrementProgress", ctx, userID, task.ID, tt.expectedPoints).Return(nil)
			}

			// Execute
			err := handler.Handle(ctx, userID, task, data)

			// Assert
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			// Verify mock expectations
			mockService.AssertExpectations(t)
		})
	}
}

func TestTradingPointsHandler_GetIdentifier(t *testing.T) {
	mockService := &MockActivityCashbackService{}
	handler := NewTradingPointsHandler(mockService)

	assert.Equal(t, model.TaskIDTradingPoints, handler.GetIdentifier())
}

func TestTradingPointsHandler_GetCategory(t *testing.T) {
	mockService := &MockActivityCashbackService{}
	handler := NewTradingPointsHandler(mockService)

	assert.Equal(t, "trading", handler.GetCategory())
}

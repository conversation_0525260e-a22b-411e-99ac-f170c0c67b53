package activity_cashback

import (
	"context"
	"time"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"go.uber.org/zap"
)

// SystemInitializer initializes the entire Activity Cashback system
type SystemInitializer struct {
	adminService         AdminServiceInterface
	backgroundJobManager *BackgroundJobManager
	taskSeeder           *TaskSeeder
	isInitialized        bool
}

// NewSystemInitializer creates a new SystemInitializer
func NewSystemInitializer() *SystemInitializer {
	return &SystemInitializer{
		adminService:         NewAdminService(),
		backgroundJobManager: NewBackgroundJobManager(),
		taskSeeder:           NewTaskSeeder(),
		isInitialized:        false,
	}
}

// Initialize initializes the entire Activity Cashback system
func (si *SystemInitializer) Initialize(ctx context.Context) error {
	if si.isInitialized {
		global.GVA_LOG.Warn("Activity Cashback system is already initialized")
		return nil
	}

	global.GVA_LOG.Info("Initializing Activity Cashback System")

	// Step 1: Seed initial tasks and categories
	if err := si.seedInitialData(ctx); err != nil {
		global.GVA_LOG.Error("Failed to seed initial data", zap.Error(err))
		return err
	}

	// Step 2: Start background jobs
	if err := si.backgroundJobManager.Start(); err != nil {
		global.GVA_LOG.Error("Failed to start background jobs", zap.Error(err))
		return err
	}

	// Step 3: Perform initial system checks
	if err := si.performSystemChecks(ctx); err != nil {
		global.GVA_LOG.Error("System checks failed", zap.Error(err))
		return err
	}

	si.isInitialized = true
	global.GVA_LOG.Info("Activity Cashback System initialized successfully")

	return nil
}

// Shutdown gracefully shuts down the Activity Cashback system
func (si *SystemInitializer) Shutdown() {
	if !si.isInitialized {
		return
	}

	global.GVA_LOG.Info("Shutting down Activity Cashback System")

	// Stop background jobs
	si.backgroundJobManager.Stop()

	si.isInitialized = false
	global.GVA_LOG.Info("Activity Cashback System shutdown completed")
}

// IsInitialized returns whether the system is initialized
func (si *SystemInitializer) IsInitialized() bool {
	return si.isInitialized
}

// GetBackgroundJobManager returns the background job manager
func (si *SystemInitializer) GetBackgroundJobManager() *BackgroundJobManager {
	return si.backgroundJobManager
}

// seedInitialData seeds initial data into the database
func (si *SystemInitializer) seedInitialData(ctx context.Context) error {
	global.GVA_LOG.Info("Seeding initial Activity Cashback data")

	// Step 1: Seed task categories first
	if err := si.seedTaskCategories(ctx); err != nil {
		return err
	}

	// Step 2: Seed initial tasks
	if err := si.taskSeeder.SeedTasks(ctx); err != nil {
		return err
	}

	global.GVA_LOG.Info("Initial data seeding completed")
	return nil
}

// seedTaskCategories seeds initial task categories
func (si *SystemInitializer) seedTaskCategories(ctx context.Context) error {
	categories := []struct {
		name        model.TaskCategoryName
		displayName string
		description string
		icon        string
		sortOrder   int
	}{
		{
			name:        model.CategoryDaily,
			displayName: model.CategoryDaily.GetDisplayName(),
			description: model.CategoryDaily.GetDescription(),
			icon:        model.CategoryDaily.GetIcon(),
			sortOrder:   model.CategoryDaily.GetSortOrder(),
		},
		{
			name:        model.CategoryCommunity,
			displayName: model.CategoryCommunity.GetDisplayName(),
			description: model.CategoryCommunity.GetDescription(),
			icon:        model.CategoryCommunity.GetIcon(),
			sortOrder:   model.CategoryCommunity.GetSortOrder(),
		},
		{
			name:        model.CategoryTrading,
			displayName: model.CategoryTrading.GetDisplayName(),
			description: model.CategoryTrading.GetDescription(),
			icon:        model.CategoryTrading.GetIcon(),
			sortOrder:   model.CategoryTrading.GetSortOrder(),
		},
	}

	for _, cat := range categories {
		// Check if category already exists
		existingCategories, err := si.adminService.GetTaskCategories(ctx)
		if err != nil {
			return err
		}

		exists := false
		for _, existing := range existingCategories {
			if existing.Name == cat.name {
				exists = true
				break
			}
		}

		if !exists {
			category := &model.TaskCategory{
				Name:        cat.name,
				DisplayName: cat.displayName,
				Description: &cat.description,
				Icon:        &cat.icon,
				IsActive:    true,
				SortOrder:   cat.sortOrder,
			}

			if err := si.adminService.CreateTaskCategory(ctx, category); err != nil {
				return err
			}

			global.GVA_LOG.Info("Task category created", zap.String("name", string(cat.name)))
		}
	}

	return nil
}

// performSystemChecks performs initial system health checks
func (si *SystemInitializer) performSystemChecks(ctx context.Context) error {
	global.GVA_LOG.Info("Performing Activity Cashback system checks")

	// Check 1: Verify task categories exist
	categories, err := si.adminService.GetTaskCategories(ctx)
	if err != nil {
		return err
	}
	if len(categories) == 0 {
		global.GVA_LOG.Warn("No task categories found")
	} else {
		global.GVA_LOG.Info("Task categories verified", zap.Int("count", len(categories)))
	}

	// Check 2: Verify tier benefits exist
	benefits, err := si.adminService.GetTierBenefits(ctx)
	if err != nil {
		return err
	}
	if len(benefits) == 0 {
		global.GVA_LOG.Warn("No tier benefits found")
	} else {
		global.GVA_LOG.Info("Tier benefits verified", zap.Int("count", len(benefits)))
	}

	// Check 3: Verify tasks exist
	tasks, err := si.adminService.GetAllTasks(ctx)
	if err != nil {
		return err
	}
	if len(tasks) == 0 {
		global.GVA_LOG.Warn("No tasks found")
	} else {
		global.GVA_LOG.Info("Tasks verified", zap.Int("count", len(tasks)))
	}

	// Check 4: Verify background jobs are running
	if !si.backgroundJobManager.IsRunning() {
		global.GVA_LOG.Error("Background jobs are not running")
		return err
	}

	global.GVA_LOG.Info("All system checks passed")
	return nil
}

// GetSystemStatus returns the current system status
func (si *SystemInitializer) GetSystemStatus(ctx context.Context) map[string]interface{} {
	status := map[string]interface{}{
		"initialized":  si.isInitialized,
		"startup_time": time.Now().UTC(),
	}

	if si.isInitialized {
		// Get task statistics
		tasks, err := si.adminService.GetAllTasks(ctx)
		if err == nil {
			status["total_tasks"] = len(tasks)

			// Count tasks by type
			tasksByCategory := make(map[string]int)
			for _, task := range tasks {
				tasksByCategory[string(task.Category.Name)]++
			}
			status["tasks_by_category"] = tasksByCategory
		}

		// Get tier statistics
		benefits, err := si.adminService.GetTierBenefits(ctx)
		if err == nil {
			status["total_tiers"] = len(benefits)
		}

		// Get tier distribution
		distribution, err := si.adminService.GetTierDistribution(ctx)
		if err == nil {
			status["tier_distribution"] = distribution
		}

		// Get background job status
		status["background_jobs"] = si.backgroundJobManager.GetJobStatus()
	}

	return status
}

// ProcessExternalEvent processes events from external systems
func (si *SystemInitializer) ProcessExternalEvent(ctx context.Context, eventType string, userID string, data map[string]interface{}) error {
	if !si.isInitialized {
		global.GVA_LOG.Warn("Activity Cashback system not initialized, ignoring event",
			zap.String("event_type", eventType),
			zap.String("user_id", userID))
		return nil
	}

	switch eventType {
	case "trade_completed":
		return si.backgroundJobManager.ProcessTradingEvent(ctx, userID, data)
	case "user_login":
		return si.backgroundJobManager.ProcessUserLogin(ctx, userID)
	case "market_check":
		return si.backgroundJobManager.ProcessMarketCheck(ctx, userID)
	default:
		global.GVA_LOG.Warn("Unknown event type", zap.String("event_type", eventType))
		return nil
	}
}

// ForceTaskReset forces a reset of all tasks (admin function)
func (si *SystemInitializer) ForceTaskReset(ctx context.Context, resetType string) error {
	if !si.isInitialized {
		return nil
	}

	global.GVA_LOG.Info("Forcing task reset", zap.String("reset_type", resetType))

	switch resetType {
	case "daily":
		return si.adminService.ResetAllDailyTasks(ctx)
	case "weekly":
		return si.adminService.ResetAllWeeklyTasks(ctx)
	case "monthly":
		return si.adminService.ResetAllMonthlyTasks(ctx)
	default:
		return nil
	}
}

// RecalculateAllTiers recalculates tiers for all users (admin function)
func (si *SystemInitializer) RecalculateAllTiers(ctx context.Context) error {
	if !si.isInitialized {
		return nil
	}

	global.GVA_LOG.Info("Recalculating all user tiers")
	return si.adminService.RecalculateAllUserTiers(ctx)
}

// GetHealthCheck returns a health check for the Activity Cashback system
func (si *SystemInitializer) GetHealthCheck(ctx context.Context) map[string]interface{} {
	health := map[string]interface{}{
		"status":      "unknown",
		"initialized": si.isInitialized,
		"timestamp":   time.Now().UTC(),
	}

	if !si.isInitialized {
		health["status"] = "not_initialized"
		return health
	}

	// Check if background jobs are running
	if !si.backgroundJobManager.IsRunning() {
		health["status"] = "unhealthy"
		health["reason"] = "background_jobs_not_running"
		return health
	}

	// Try to get basic data to verify database connectivity
	_, err := si.adminService.GetTaskCategories(ctx)
	if err != nil {
		health["status"] = "unhealthy"
		health["reason"] = "database_connectivity_issue"
		health["error"] = err.Error()
		return health
	}

	health["status"] = "healthy"
	return health
}

// Global instance for easy access
var globalSystemInitializer *SystemInitializer

// InitializeGlobalSystem initializes the global Activity Cashback system
func InitializeGlobalSystem(ctx context.Context) error {
	if globalSystemInitializer == nil {
		globalSystemInitializer = NewSystemInitializer()
	}
	return globalSystemInitializer.Initialize(ctx)
}

// ShutdownGlobalSystem shuts down the global Activity Cashback system
func ShutdownGlobalSystem() {
	if globalSystemInitializer != nil {
		globalSystemInitializer.Shutdown()
	}
}

// GetGlobalSystemInitializer returns the global system initializer
func GetGlobalSystemInitializer() *SystemInitializer {
	return globalSystemInitializer
}

// ProcessGlobalEvent processes events through the global system
func ProcessGlobalEvent(ctx context.Context, eventType string, userID string, data map[string]interface{}) error {
	if globalSystemInitializer == nil {
		return nil
	}
	return globalSystemInitializer.ProcessExternalEvent(ctx, eventType, userID, data)
}

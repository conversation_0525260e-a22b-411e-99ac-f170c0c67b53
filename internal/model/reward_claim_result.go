package model

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// RewardClaimResult represents the result status of a reward claim
type RewardClaimResult string

const (
	RewardClaimResultPending    RewardClaimResult = "pending"
	RewardClaimResultProcessing RewardClaimResult = "processing"
	RewardClaimResultSuccess    RewardClaimResult = "success"
	RewardClaimResultFailed     RewardClaimResult = "failed"
)

// RewardClaimType represents the type of reward claim
type RewardClaimType string

const (
	RewardClaimMemeTypeCashback   RewardClaimType = "meme_cashback"
	RewardClaimMemeTypeCommission RewardClaimType = "meme_commission"
)

// RewardClaimResultRecord represents the reward_claim_results table
type RewardClaimResultRecord struct {
	ID               uuid.UUID         `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID           uuid.UUID         `gorm:"type:uuid;not null;index" json:"user_id"`
	Address          string            `gorm:"type:varchar(255);not null" json:"address"`
	Amount           decimal.Decimal   `gorm:"type:decimal(36,18);not null" json:"amount"`
	AmountUsd        decimal.Decimal   `gorm:"type:decimal(36,18);not null" json:"amount_usd"`
	Token            string            `gorm:"type:varchar(255);not null" json:"token"`
	ChainID          int               `gorm:"type:int;not null" json:"chain_id"`
	Type             RewardClaimType   `gorm:"type:varchar(20);not null" json:"type"`
	Result           RewardClaimResult `gorm:"type:varchar(20);not null" json:"result"`
	TransactionHash  *string           `gorm:"type:varchar(255)" json:"transaction_hash,omitempty"`
	OnchainTimestamp *int64            `gorm:"type:bigint" json:"onchain_timestamp,omitempty"`
	ErrorMessage     *string           `gorm:"type:text" json:"error_message,omitempty"`
	ErrorCode        *string           `gorm:"type:varchar(100)" json:"error_code,omitempty"`
	ProcessedAt      *time.Time        `gorm:"nullable" json:"processed_at"`
	CreatedAt        time.Time         `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt        time.Time         `gorm:"default:CURRENT_TIMESTAMP" json:"updated_at"`

	// Relationships
	User User `gorm:"foreignKey:UserID;references:ID" json:"user,omitempty"`
}

// TableName specifies the table name for RewardClaimResultRecord
func (RewardClaimResultRecord) TableName() string {
	return "reward_claim_results"
}

// IsPending checks if the claim result is pending
func (rcr *RewardClaimResultRecord) IsPending() bool {
	return rcr.Result == RewardClaimResultPending
}

// IsProcessing checks if the claim result is being processed
func (rcr *RewardClaimResultRecord) IsProcessing() bool {
	return rcr.Result == RewardClaimResultProcessing
}

// IsSuccess checks if the claim result was successful
func (rcr *RewardClaimResultRecord) IsSuccess() bool {
	return rcr.Result == RewardClaimResultSuccess
}

// IsFailed checks if the claim result failed
func (rcr *RewardClaimResultRecord) IsFailed() bool {
	return rcr.Result == RewardClaimResultFailed
}

// HasError checks if the claim result has error information
func (rcr *RewardClaimResultRecord) HasError() bool {
	return rcr.ErrorMessage != nil || rcr.ErrorCode != nil
}

// IsCashbackReward checks if the claim is for cashback rewards
func (rcr *RewardClaimResultRecord) IsMemeCashbackReward() bool {
	return rcr.Type == RewardClaimMemeTypeCashback
}

// IsCommissionReward checks if the claim is for commission rewards
func (rcr *RewardClaimResultRecord) IsMemeCommissionReward() bool {
	return rcr.Type == RewardClaimMemeTypeCommission
}
